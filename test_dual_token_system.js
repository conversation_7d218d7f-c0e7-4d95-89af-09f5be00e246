// Test script for the dual token system implementation
// This tests the new initialTokens and receivedTokens logic

// Mock the TOKEN_RULES for testing
const TOKEN_RULES = {
  INITIAL_TOKENS_PER_TEAM: 11,
  TOTAL_TOKENS: 22,
  BID_LESS_THAN_200: { SUCCESS: 1, FAILURE: 2 },
  BID_200_TO_249: { SUCCESS: 2, FAILURE: 3 },
  BID_250_OR_MORE: { SUCCESS: 3, FAILURE: 4 }
};

// Mock partnership with dual token system
function createMockPartnership() {
  return {
    team1: {
      id: 'team1',
      name: 'Team 1',
      playerIds: ['player1', 'player3'],
      score: 0,
      tricksWon: 0,
      roundScore: 0,
      tokens: 11, // Total tokens (for backward compatibility)
      initialTokens: 11, // Remaining from starting 11
      receivedTokens: 0, // Tokens received from opponents
      consecutiveWins: 0,
      totalRoundsWon: 0
    },
    team2: {
      id: 'team2',
      name: 'Team 2',
      playerIds: ['player2', 'player4'],
      score: 0,
      tricksWon: 0,
      roundScore: 0,
      tokens: 11, // Total tokens (for backward compatibility)
      initialTokens: 11, // Remaining from starting 11
      receivedTokens: 0, // Tokens received from opponents
      consecutiveWins: 0,
      totalRoundsWon: 0
    }
  };
}

// Simulate token transfer using dual system
function simulateTokenTransfer(partnership, winningTeamId, losingTeamId, tokensToTransfer) {
  console.log(`\n🔄 Transferring ${tokensToTransfer} tokens from ${losingTeamId} to ${winningTeamId}`);
  
  console.log('Before transfer:');
  console.log(`  ${partnership.team1.name}: ${partnership.team1.initialTokens} initial + ${partnership.team1.receivedTokens} received = ${partnership.team1.tokens} total`);
  console.log(`  ${partnership.team2.name}: ${partnership.team2.initialTokens} initial + ${partnership.team2.receivedTokens} received = ${partnership.team2.tokens} total`);
  
  // Update losing team: decrease initial tokens
  partnership[losingTeamId].initialTokens = Math.max(
    0,
    partnership[losingTeamId].initialTokens - tokensToTransfer
  );
  
  // Update winning team: increase received tokens
  partnership[winningTeamId].receivedTokens = Math.min(
    TOKEN_RULES.INITIAL_TOKENS_PER_TEAM,
    partnership[winningTeamId].receivedTokens + tokensToTransfer
  );
  
  // Update total tokens for backward compatibility
  partnership[winningTeamId].tokens = 
    partnership[winningTeamId].initialTokens + partnership[winningTeamId].receivedTokens;
  partnership[losingTeamId].tokens = 
    partnership[losingTeamId].initialTokens + partnership[losingTeamId].receivedTokens;
  
  console.log('After transfer:');
  console.log(`  ${partnership.team1.name}: ${partnership.team1.initialTokens} initial + ${partnership.team1.receivedTokens} received = ${partnership.team1.tokens} total`);
  console.log(`  ${partnership.team2.name}: ${partnership.team2.initialTokens} initial + ${partnership.team2.receivedTokens} received = ${partnership.team2.tokens} total`);
  
  // Verify total tokens remain constant
  const totalTokens = partnership.team1.tokens + partnership.team2.tokens;
  console.log(`  Total tokens in game: ${totalTokens} (should be ${TOKEN_RULES.TOTAL_TOKENS})`);
  
  return totalTokens === TOKEN_RULES.TOTAL_TOKENS;
}

// Check game end condition using dual system
function checkGameEndCondition(partnership) {
  const team1Eliminated = partnership.team1.initialTokens <= 0;
  const team2Eliminated = partnership.team2.initialTokens <= 0;
  
  if (team1Eliminated) {
    return { isGameComplete: true, winner: 'team2', reason: 'Team 1 eliminated (0 initial tokens)' };
  }
  
  if (team2Eliminated) {
    return { isGameComplete: true, winner: 'team1', reason: 'Team 2 eliminated (0 initial tokens)' };
  }
  
  return { isGameComplete: false };
}

// Run tests
console.log('🧪 Testing Dual Token System Implementation');
console.log('='.repeat(50));

const partnership = createMockPartnership();

// Test 1: Team 1 wins 2 tokens
console.log('\n📋 Test 1: Team 1 wins 2 tokens (successful bid 200-249)');
const test1Valid = simulateTokenTransfer(partnership, 'team1', 'team2', 2);
console.log(`✅ Token conservation: ${test1Valid ? 'PASS' : 'FAIL'}`);

// Test 2: Team 2 wins 3 tokens
console.log('\n📋 Test 2: Team 2 wins 3 tokens (successful bid 250+)');
const test2Valid = simulateTokenTransfer(partnership, 'team2', 'team1', 3);
console.log(`✅ Token conservation: ${test2Valid ? 'PASS' : 'FAIL'}`);

// Test 3: Team 1 wins 4 tokens
console.log('\n📋 Test 3: Team 1 wins 4 tokens (failed bid 250+)');
const test3Valid = simulateTokenTransfer(partnership, 'team1', 'team2', 4);
console.log(`✅ Token conservation: ${test3Valid ? 'PASS' : 'FAIL'}`);

// Test 4: Check game end condition
console.log('\n📋 Test 4: Check game end condition');
const gameEnd = checkGameEndCondition(partnership);
console.log(`Game status: ${gameEnd.isGameComplete ? gameEnd.reason : 'Game continues'}`);

// Test 5: Simulate game end scenario
console.log('\n📋 Test 5: Simulate game end scenario');
simulateTokenTransfer(partnership, 'team1', 'team2', 2); // Team 2 should be eliminated
const finalGameEnd = checkGameEndCondition(partnership);
console.log(`Final game status: ${finalGameEnd.isGameComplete ? finalGameEnd.reason : 'Game continues'}`);

console.log('\n🎯 Summary:');
console.log(`  Team 1: ${partnership.team1.initialTokens} initial + ${partnership.team1.receivedTokens} received = ${partnership.team1.tokens} total`);
console.log(`  Team 2: ${partnership.team2.initialTokens} initial + ${partnership.team2.receivedTokens} received = ${partnership.team2.tokens} total`);
console.log(`  Game complete: ${finalGameEnd.isGameComplete}`);
if (finalGameEnd.isGameComplete) {
  console.log(`  Winner: ${finalGameEnd.winner}`);
  console.log(`  Reason: ${finalGameEnd.reason}`);
}
