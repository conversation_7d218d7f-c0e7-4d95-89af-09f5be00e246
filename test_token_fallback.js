// Test script for token fallback logic in TokenScoreDisplay
// This simulates existing partnerships without the new fields

console.log('🧪 Testing Token Fallback Logic');
console.log('='.repeat(50));

// Simulate existing partnership (without initialTokens and receivedTokens)
const existingPartnership = {
  team1: {
    id: 'team1',
    name: 'Team 1',
    playerIds: ['player1', 'player3'],
    score: 150,
    tricksWon: 3,
    roundScore: 75,
    tokens: 9, // Only has tokens field
    consecutiveWins: 1,
    totalRoundsWon: 2
    // Missing: initialTokens, receivedTokens
  },
  team2: {
    id: 'team2',
    name: 'Team 2',
    playerIds: ['player2', 'player4'],
    score: 120,
    tricksWon: 5,
    roundScore: 60,
    tokens: 13, // Only has tokens field
    consecutiveWins: 0,
    totalRoundsWon: 1
    // Missing: initialTokens, receivedTokens
  }
};

// Simulate new partnership (with all fields)
const newPartnership = {
  team1: {
    id: 'team1',
    name: 'Team 1',
    playerIds: ['player1', 'player3'],
    score: 0,
    tricksWon: 0,
    roundScore: 0,
    tokens: 11,
    initialTokens: 11,
    receivedTokens: 0,
    consecutiveWins: 0,
    totalRoundsWon: 0
  },
  team2: {
    id: 'team2',
    name: 'Team 2',
    playerIds: ['player2', 'player4'],
    score: 0,
    tricksWon: 0,
    roundScore: 0,
    tokens: 11,
    initialTokens: 11,
    receivedTokens: 0,
    consecutiveWins: 0,
    totalRoundsWon: 0
  }
};

// Simulate the fallback functions from TokenScoreDisplay
function getInitialTokens(team) {
  // If initialTokens exists, use it
  if (typeof team.initialTokens === 'number') {
    return team.initialTokens;
  }
  // Fallback: if only tokens field exists, assume all are initial tokens for existing partnerships
  if (typeof team.tokens === 'number') {
    return team.tokens;
  }
  // Default fallback
  return 11;
}

function getReceivedTokens(team) {
  // If receivedTokens exists, use it
  if (typeof team.receivedTokens === 'number') {
    return team.receivedTokens;
  }
  // Fallback: if this is an existing partnership without dual token system, return 0
  return 0;
}

// Test existing partnership
console.log('\n📋 Test 1: Existing Partnership (no dual token fields)');
console.log('Team 1:');
console.log(`  Raw tokens: ${existingPartnership.team1.tokens}`);
console.log(`  Initial tokens (fallback): ${getInitialTokens(existingPartnership.team1)}`);
console.log(`  Received tokens (fallback): ${getReceivedTokens(existingPartnership.team1)}`);

console.log('Team 2:');
console.log(`  Raw tokens: ${existingPartnership.team2.tokens}`);
console.log(`  Initial tokens (fallback): ${getInitialTokens(existingPartnership.team2)}`);
console.log(`  Received tokens (fallback): ${getReceivedTokens(existingPartnership.team2)}`);

// Test new partnership
console.log('\n📋 Test 2: New Partnership (with dual token fields)');
console.log('Team 1:');
console.log(`  Raw tokens: ${newPartnership.team1.tokens}`);
console.log(`  Initial tokens: ${getInitialTokens(newPartnership.team1)}`);
console.log(`  Received tokens: ${getReceivedTokens(newPartnership.team1)}`);

console.log('Team 2:');
console.log(`  Raw tokens: ${newPartnership.team2.tokens}`);
console.log(`  Initial tokens: ${getInitialTokens(newPartnership.team2)}`);
console.log(`  Received tokens: ${getReceivedTokens(newPartnership.team2)}`);

// Test edge cases
console.log('\n📋 Test 3: Edge Cases');

// Team with no tokens field
const brokenTeam = { id: 'team1', name: 'Broken Team' };
console.log('Broken team (no tokens field):');
console.log(`  Initial tokens (fallback): ${getInitialTokens(brokenTeam)}`);
console.log(`  Received tokens (fallback): ${getReceivedTokens(brokenTeam)}`);

// Team with zero tokens
const zeroTokenTeam = { tokens: 0, initialTokens: 0, receivedTokens: 0 };
console.log('Zero token team:');
console.log(`  Initial tokens: ${getInitialTokens(zeroTokenTeam)}`);
console.log(`  Received tokens: ${getReceivedTokens(zeroTokenTeam)}`);

console.log('\n✅ All fallback tests completed successfully!');
console.log('The TokenScoreDisplay component should now handle both existing and new partnerships.');
