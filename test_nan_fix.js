// Test script to verify the NaN fix for token transfers
// This simulates the exact scenario that was causing NaN values

console.log('🧪 Testing NaN Fix for Token Transfers');
console.log('='.repeat(50));

// Mock TOKEN_RULES
const TOKEN_RULES = {
  INITIAL_TOKENS_PER_TEAM: 11,
  TOTAL_TOKENS: 22,
  BID_LESS_THAN_200: { SUCCESS: 1, FAILURE: 2 },
  BID_200_TO_249: { SUCCESS: 2, FAILURE: 3 },
  BID_250_OR_MORE: { SUCCESS: 3, FAILURE: 4 }
};

// Simulate existing partnership (the problematic case)
const existingPartnership = {
  team1: {
    id: 'team1',
    name: 'Team 1',
    playerIds: ['player1', 'player3'],
    score: 150,
    tricksWon: 3,
    roundScore: 75,
    tokens: 9, // Only has tokens field
    consecutiveWins: 1,
    totalRoundsWon: 2
    // Missing: initialTokens, receivedTokens
  },
  team2: {
    id: 'team2',
    name: 'Team 2',
    playerIds: ['player2', 'player4'],
    score: 120,
    tricksWon: 5,
    roundScore: 60,
    tokens: 13, // Only has tokens field
    consecutiveWins: 0,
    totalRoundsWon: 1
    // Missing: initialTokens, receivedTokens
  }
};

// Migration function (from gameService.ts)
function migrateToDualTokenSystem(partnership) {
  const migratedPartnership = { ...partnership };
  
  // Migrate team1 if needed
  if (typeof migratedPartnership.team1.initialTokens !== 'number') {
    migratedPartnership.team1.initialTokens = migratedPartnership.team1.tokens || TOKEN_RULES.INITIAL_TOKENS_PER_TEAM;
  }
  if (typeof migratedPartnership.team1.receivedTokens !== 'number') {
    migratedPartnership.team1.receivedTokens = 0;
  }
  
  // Migrate team2 if needed
  if (typeof migratedPartnership.team2.initialTokens !== 'number') {
    migratedPartnership.team2.initialTokens = migratedPartnership.team2.tokens || TOKEN_RULES.INITIAL_TOKENS_PER_TEAM;
  }
  if (typeof migratedPartnership.team2.receivedTokens !== 'number') {
    migratedPartnership.team2.receivedTokens = 0;
  }
  
  // Update total tokens for consistency
  migratedPartnership.team1.tokens = migratedPartnership.team1.initialTokens + migratedPartnership.team1.receivedTokens;
  migratedPartnership.team2.tokens = migratedPartnership.team2.initialTokens + migratedPartnership.team2.receivedTokens;
  
  return migratedPartnership;
}

// Simulate the problematic token transfer (OLD WAY - causes NaN)
function simulateOldTokenTransfer(partnership, winningTeamId, losingTeamId, tokensToTransfer) {
  console.log('\n❌ OLD WAY (causes NaN):');
  console.log(`Transferring ${tokensToTransfer} tokens from ${losingTeamId} to ${winningTeamId}`);
  
  const updatedPartnership = { ...partnership };
  
  console.log('Before transfer:');
  console.log(`  Team1 initialTokens: ${updatedPartnership.team1.initialTokens} (${typeof updatedPartnership.team1.initialTokens})`);
  console.log(`  Team1 receivedTokens: ${updatedPartnership.team1.receivedTokens} (${typeof updatedPartnership.team1.receivedTokens})`);
  console.log(`  Team2 initialTokens: ${updatedPartnership.team2.initialTokens} (${typeof updatedPartnership.team2.initialTokens})`);
  console.log(`  Team2 receivedTokens: ${updatedPartnership.team2.receivedTokens} (${typeof updatedPartnership.team2.receivedTokens})`);
  
  // This causes NaN because initialTokens and receivedTokens are undefined
  updatedPartnership[losingTeamId].initialTokens = Math.max(
    0,
    updatedPartnership[losingTeamId].initialTokens - tokensToTransfer
  );
  
  updatedPartnership[winningTeamId].receivedTokens = Math.min(
    TOKEN_RULES.INITIAL_TOKENS_PER_TEAM,
    updatedPartnership[winningTeamId].receivedTokens + tokensToTransfer
  );
  
  updatedPartnership[winningTeamId].tokens =
    updatedPartnership[winningTeamId].initialTokens + updatedPartnership[winningTeamId].receivedTokens;
  updatedPartnership[losingTeamId].tokens =
    updatedPartnership[losingTeamId].initialTokens + updatedPartnership[losingTeamId].receivedTokens;
  
  console.log('After transfer:');
  console.log(`  Team1 tokens: ${updatedPartnership.team1.tokens}`);
  console.log(`  Team2 tokens: ${updatedPartnership.team2.tokens}`);
  
  return updatedPartnership;
}

// Simulate the fixed token transfer (NEW WAY - with migration)
function simulateNewTokenTransfer(partnership, winningTeamId, losingTeamId, tokensToTransfer) {
  console.log('\n✅ NEW WAY (with migration):');
  console.log(`Transferring ${tokensToTransfer} tokens from ${losingTeamId} to ${winningTeamId}`);
  
  let updatedPartnership = { ...partnership };
  
  // MIGRATION STEP - this is the fix!
  updatedPartnership = migrateToDualTokenSystem(updatedPartnership);
  
  console.log('After migration:');
  console.log(`  Team1 initialTokens: ${updatedPartnership.team1.initialTokens} (${typeof updatedPartnership.team1.initialTokens})`);
  console.log(`  Team1 receivedTokens: ${updatedPartnership.team1.receivedTokens} (${typeof updatedPartnership.team1.receivedTokens})`);
  console.log(`  Team2 initialTokens: ${updatedPartnership.team2.initialTokens} (${typeof updatedPartnership.team2.initialTokens})`);
  console.log(`  Team2 receivedTokens: ${updatedPartnership.team2.receivedTokens} (${typeof updatedPartnership.team2.receivedTokens})`);
  
  // Now this works correctly because all fields are numbers
  updatedPartnership[losingTeamId].initialTokens = Math.max(
    0,
    updatedPartnership[losingTeamId].initialTokens - tokensToTransfer
  );
  
  updatedPartnership[winningTeamId].receivedTokens = Math.min(
    TOKEN_RULES.INITIAL_TOKENS_PER_TEAM,
    updatedPartnership[winningTeamId].receivedTokens + tokensToTransfer
  );
  
  updatedPartnership[winningTeamId].tokens =
    updatedPartnership[winningTeamId].initialTokens + updatedPartnership[winningTeamId].receivedTokens;
  updatedPartnership[losingTeamId].tokens =
    updatedPartnership[losingTeamId].initialTokens + updatedPartnership[losingTeamId].receivedTokens;
  
  console.log('After transfer:');
  console.log(`  Team1 tokens: ${updatedPartnership.team1.tokens}`);
  console.log(`  Team2 tokens: ${updatedPartnership.team2.tokens}`);
  
  return updatedPartnership;
}

// Test the scenarios
console.log('\n📋 Testing with existing partnership (team1 wins 2 tokens):');

// Test old way (should produce NaN)
const oldResult = simulateOldTokenTransfer(existingPartnership, 'team1', 'team2', 2);
const hasNaN = isNaN(oldResult.team1.tokens) || isNaN(oldResult.team2.tokens);
console.log(`\nOld way produces NaN: ${hasNaN ? '❌ YES' : '✅ NO'}`);

// Test new way (should work correctly)
const newResult = simulateNewTokenTransfer(existingPartnership, 'team1', 'team2', 2);
const hasNaNNew = isNaN(newResult.team1.tokens) || isNaN(newResult.team2.tokens);
console.log(`New way produces NaN: ${hasNaNNew ? '❌ YES' : '✅ NO'}`);

// Verify token conservation
const totalTokens = newResult.team1.tokens + newResult.team2.tokens;
console.log(`\nToken conservation: ${totalTokens === TOKEN_RULES.TOTAL_TOKENS ? '✅ PASS' : '❌ FAIL'} (${totalTokens}/22)`);

console.log('\n🎯 Summary:');
console.log('- Migration function prevents NaN by ensuring all token fields exist');
console.log('- Existing partnerships are automatically migrated during token transfers');
console.log('- Token conservation is maintained');
console.log('- The fix is backward compatible');
